"""
Database models and operations for the arbitrage bot.
"""
import asyncio
import aiosqlite
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import json

from src.config.settings import config


@dataclass
class PriceData:
    """Price data model."""
    exchange: str
    symbol: str
    price: float
    timestamp: datetime
    volume: Optional[float] = None
    bid: Optional[float] = None
    ask: Optional[float] = None


@dataclass
class ArbitrageSignal:
    """Arbitrage signal model."""
    id: Optional[int] = None
    symbol: str = ""
    buy_exchange: str = ""
    sell_exchange: str = ""
    buy_price: float = 0.0
    sell_price: float = 0.0
    profit_percent: float = 0.0
    profit_amount: float = 0.0
    action: str = ""  # "LONG" or "SHORT"
    timestamp: datetime = None
    sent_to_telegram: bool = False


class DatabaseManager:
    """Database manager for SQLite operations."""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path or config.database.path
        self.db_file = Path(self.db_path)
        self.db_file.parent.mkdir(parents=True, exist_ok=True)
        
    async def initialize(self):
        """Initialize database tables."""
        async with aiosqlite.connect(self.db_path) as db:
            # Create prices table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS prices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    price REAL NOT NULL,
                    volume REAL,
                    bid REAL,
                    ask REAL,
                    timestamp DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create arbitrage_signals table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS arbitrage_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    buy_exchange TEXT NOT NULL,
                    sell_exchange TEXT NOT NULL,
                    buy_price REAL NOT NULL,
                    sell_price REAL NOT NULL,
                    profit_percent REAL NOT NULL,
                    profit_amount REAL NOT NULL,
                    action TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    sent_to_telegram BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create exchange_status table for monitoring
            await db.execute("""
                CREATE TABLE IF NOT EXISTS exchange_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exchange TEXT NOT NULL,
                    status TEXT NOT NULL,
                    last_update DATETIME NOT NULL,
                    error_message TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for better performance
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_prices_symbol_timestamp 
                ON prices(symbol, timestamp)
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_prices_exchange_symbol 
                ON prices(exchange, symbol)
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_signals_timestamp 
                ON arbitrage_signals(timestamp)
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_signals_symbol 
                ON arbitrage_signals(symbol)
            """)
            
            await db.commit()
    
    async def insert_price(self, price_data: PriceData):
        """Insert price data into database."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO prices (exchange, symbol, price, volume, bid, ask, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                price_data.exchange,
                price_data.symbol,
                price_data.price,
                price_data.volume,
                price_data.bid,
                price_data.ask,
                price_data.timestamp
            ))
            await db.commit()
    
    async def insert_prices_batch(self, price_data_list: List[PriceData]):
        """Insert multiple price data records in batch."""
        async with aiosqlite.connect(self.db_path) as db:
            data = [
                (
                    pd.exchange, pd.symbol, pd.price, pd.volume,
                    pd.bid, pd.ask, pd.timestamp
                )
                for pd in price_data_list
            ]
            await db.executemany("""
                INSERT INTO prices (exchange, symbol, price, volume, bid, ask, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, data)
            await db.commit()
    
    async def insert_arbitrage_signal(self, signal: ArbitrageSignal) -> int:
        """Insert arbitrage signal and return the ID."""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                INSERT INTO arbitrage_signals 
                (symbol, buy_exchange, sell_exchange, buy_price, sell_price, 
                 profit_percent, profit_amount, action, timestamp, sent_to_telegram)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                signal.symbol,
                signal.buy_exchange,
                signal.sell_exchange,
                signal.buy_price,
                signal.sell_price,
                signal.profit_percent,
                signal.profit_amount,
                signal.action,
                signal.timestamp,
                signal.sent_to_telegram
            ))
            await db.commit()
            return cursor.lastrowid
    
    async def update_signal_telegram_status(self, signal_id: int, sent: bool = True):
        """Update telegram sent status for a signal."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                UPDATE arbitrage_signals 
                SET sent_to_telegram = ? 
                WHERE id = ?
            """, (sent, signal_id))
            await db.commit()
    
    async def get_latest_prices(self, symbol: str, limit: int = 10) -> List[PriceData]:
        """Get latest prices for a symbol across all exchanges."""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT exchange, symbol, price, volume, bid, ask, timestamp
                FROM prices 
                WHERE symbol = ?
                ORDER BY timestamp DESC
                LIMIT ?
            """, (symbol, limit))
            
            rows = await cursor.fetchall()
            return [
                PriceData(
                    exchange=row[0],
                    symbol=row[1],
                    price=row[2],
                    volume=row[3],
                    bid=row[4],
                    ask=row[5],
                    timestamp=datetime.fromisoformat(row[6])
                )
                for row in rows
            ]
    
    async def get_latest_price_by_exchange(self, symbol: str, exchange: str) -> Optional[PriceData]:
        """Get latest price for a symbol from specific exchange."""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT exchange, symbol, price, volume, bid, ask, timestamp
                FROM prices 
                WHERE symbol = ? AND exchange = ?
                ORDER BY timestamp DESC
                LIMIT 1
            """, (symbol, exchange))
            
            row = await cursor.fetchone()
            if row:
                return PriceData(
                    exchange=row[0],
                    symbol=row[1],
                    price=row[2],
                    volume=row[3],
                    bid=row[4],
                    ask=row[5],
                    timestamp=datetime.fromisoformat(row[6])
                )
            return None
    
    async def get_recent_signals(self, symbol: str, hours: int = 24) -> List[ArbitrageSignal]:
        """Get recent arbitrage signals for a symbol."""
        since = datetime.now() - timedelta(hours=hours)
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT id, symbol, buy_exchange, sell_exchange, buy_price, sell_price,
                       profit_percent, profit_amount, action, timestamp, sent_to_telegram
                FROM arbitrage_signals 
                WHERE symbol = ? AND timestamp > ?
                ORDER BY timestamp DESC
            """, (symbol, since))
            
            rows = await cursor.fetchall()
            return [
                ArbitrageSignal(
                    id=row[0],
                    symbol=row[1],
                    buy_exchange=row[2],
                    sell_exchange=row[3],
                    buy_price=row[4],
                    sell_price=row[5],
                    profit_percent=row[6],
                    profit_amount=row[7],
                    action=row[8],
                    timestamp=datetime.fromisoformat(row[9]),
                    sent_to_telegram=bool(row[10])
                )
                for row in rows
            ]
    
    async def check_recent_signal(self, symbol: str, buy_exchange: str, 
                                 sell_exchange: str, minutes: int = 5) -> bool:
        """Check if similar signal was sent recently to avoid spam."""
        since = datetime.now() - timedelta(minutes=minutes)
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT COUNT(*) FROM arbitrage_signals 
                WHERE symbol = ? AND buy_exchange = ? AND sell_exchange = ? 
                AND timestamp > ? AND sent_to_telegram = TRUE
            """, (symbol, buy_exchange, sell_exchange, since))
            
            count = await cursor.fetchone()
            return count[0] > 0
    
    async def update_exchange_status(self, exchange: str, status: str, 
                                   error_message: Optional[str] = None):
        """Update exchange status for monitoring."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT OR REPLACE INTO exchange_status 
                (exchange, status, last_update, error_message)
                VALUES (?, ?, ?, ?)
            """, (exchange, status, datetime.now(), error_message))
            await db.commit()
    
    async def cleanup_old_data(self, days: int = 30):
        """Clean up old data to keep database size manageable."""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        async with aiosqlite.connect(self.db_path) as db:
            # Clean old prices
            await db.execute("""
                DELETE FROM prices WHERE timestamp < ?
            """, (cutoff_date,))
            
            # Clean old signals (keep longer for analysis)
            signal_cutoff = datetime.now() - timedelta(days=days * 2)
            await db.execute("""
                DELETE FROM arbitrage_signals WHERE timestamp < ?
            """, (signal_cutoff,))
            
            # Clean old exchange status
            await db.execute("""
                DELETE FROM exchange_status WHERE created_at < ?
            """, (cutoff_date,))
            
            await db.commit()
    
    async def get_statistics(self) -> Dict:
        """Get database statistics."""
        async with aiosqlite.connect(self.db_path) as db:
            stats = {}
            
            # Price records count
            cursor = await db.execute("SELECT COUNT(*) FROM prices")
            stats['total_prices'] = (await cursor.fetchone())[0]
            
            # Signal records count
            cursor = await db.execute("SELECT COUNT(*) FROM arbitrage_signals")
            stats['total_signals'] = (await cursor.fetchone())[0]
            
            # Recent signals (24h)
            since_24h = datetime.now() - timedelta(hours=24)
            cursor = await db.execute("""
                SELECT COUNT(*) FROM arbitrage_signals WHERE timestamp > ?
            """, (since_24h,))
            stats['signals_24h'] = (await cursor.fetchone())[0]
            
            # Signals by symbol
            cursor = await db.execute("""
                SELECT symbol, COUNT(*) FROM arbitrage_signals 
                GROUP BY symbol ORDER BY COUNT(*) DESC
            """)
            stats['signals_by_symbol'] = dict(await cursor.fetchall())
            
            return stats


# Global database instance
db_manager = DatabaseManager()
