"""
Configuration management for the arbitrage bot.
"""
import os
from pathlib import Path
from typing import Dict, List, Optional
import yaml
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class BotConfig(BaseModel):
    """Bot configuration settings."""
    name: str = "Crypto Arbitrage Bot"
    version: str = "1.0.0"
    monitoring_interval: int = 15
    profit_threshold: float = 0.02
    max_signal_frequency: int = 300


class TradingPair(BaseModel):
    """Trading pair configuration."""
    symbol: str
    enabled: bool = True
    min_profit: float = 0.02


class ExchangeConfig(BaseModel):
    """Exchange configuration."""
    enabled: bool = True
    rate_limit: int = 1000
    timeout: int = 10
    type: str = 'real'
    api_urls: List[str] = []


class DatabaseConfig(BaseModel):
    """Database configuration."""
    path: str = "data/arbitrage.db"
    backup_interval: int = 3600
    cleanup_days: int = 30


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = "INFO"
    file_path: str = "logs/arbitrage.log"
    max_file_size: str = "10MB"
    backup_count: int = 5
    console_output: bool = True


class TelegramConfig(BaseModel):
    """Telegram configuration."""
    enabled: bool = True
    parse_mode: str = "HTML"
    disable_web_page_preview: bool = True
    message_template: str = ""


class Settings(BaseSettings):
    """Main settings class with environment variable support."""
    
    # Telegram settings from environment
    telegram_bot_token: Optional[str] = Field(None, env="TELEGRAM_BOT_TOKEN")
    telegram_chat_id: Optional[str] = Field(None, env="TELEGRAM_CHAT_ID")
    
    # Optional database override
    database_path: Optional[str] = Field(None, env="DATABASE_PATH")
    
    # Optional logging overrides
    log_level: Optional[str] = Field(None, env="LOG_LEVEL")
    log_file: Optional[str] = Field(None, env="LOG_FILE")
    
    # Exchange API keys (optional)
    mexc_api_key: Optional[str] = Field(None, env="MEXC_API_KEY")
    mexc_secret: Optional[str] = Field(None, env="MEXC_SECRET")
    gate_api_key: Optional[str] = Field(None, env="GATE_API_KEY")
    gate_secret: Optional[str] = Field(None, env="GATE_SECRET")
    lbank_api_key: Optional[str] = Field(None, env="LBANK_API_KEY")
    lbank_secret: Optional[str] = Field(None, env="LBANK_SECRET")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class ConfigManager:
    """Configuration manager for loading and managing bot settings."""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = Path(config_path)
        self.settings = Settings()
        self._config_data = self._load_config()
        
    def _load_config(self) -> Dict:
        """Load configuration from YAML file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
        with open(self.config_path, 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)
    
    @property
    def bot(self) -> BotConfig:
        """Get bot configuration."""
        return BotConfig(**self._config_data.get('bot', {}))
    
    @property
    def trading_pairs(self) -> List[TradingPair]:
        """Get trading pairs configuration."""
        pairs_data = self._config_data.get('trading_pairs', [])
        return [TradingPair(**pair) for pair in pairs_data]
    
    @property
    def exchanges(self) -> Dict[str, ExchangeConfig]:
        """Get exchanges configuration."""
        exchanges_data = self._config_data.get('exchanges', {})
        return {
            name: ExchangeConfig(**config) 
            for name, config in exchanges_data.items()
        }
    
    @property
    def database(self) -> DatabaseConfig:
        """Get database configuration."""
        db_config = DatabaseConfig(**self._config_data.get('database', {}))
        # Override with environment variable if set
        if self.settings.database_path:
            db_config.path = self.settings.database_path
        return db_config
    
    @property
    def logging(self) -> LoggingConfig:
        """Get logging configuration."""
        log_config = LoggingConfig(**self._config_data.get('logging', {}))
        # Override with environment variables if set
        if self.settings.log_level:
            log_config.level = self.settings.log_level
        if self.settings.log_file:
            log_config.file_path = self.settings.log_file
        return log_config
    
    @property
    def telegram(self) -> TelegramConfig:
        """Get telegram configuration."""
        return TelegramConfig(**self._config_data.get('telegram', {}))
    
    def get_exchange_credentials(self, exchange_name: str) -> Dict[str, Optional[str]]:
        """Get exchange API credentials from environment variables."""
        credentials = {
            'apiKey': None,
            'secret': None,
        }
        
        if exchange_name.lower() == 'mexc':
            credentials['apiKey'] = self.settings.mexc_api_key
            credentials['secret'] = self.settings.mexc_secret
        elif exchange_name.lower() == 'gate':
            credentials['apiKey'] = self.settings.gate_api_key
            credentials['secret'] = self.settings.gate_secret
        elif exchange_name.lower() == 'lbank':
            credentials['apiKey'] = self.settings.lbank_api_key
            credentials['secret'] = self.settings.lbank_secret
            
        return credentials
    
    def is_telegram_configured(self) -> bool:
        """Check if Telegram is properly configured."""
        return (
            self.telegram.enabled and 
            self.settings.telegram_bot_token is not None and 
            self.settings.telegram_chat_id is not None
        )
    
    def reload_config(self):
        """Reload configuration from file."""
        self._config_data = self._load_config()


# Global configuration instance
config = ConfigManager()
